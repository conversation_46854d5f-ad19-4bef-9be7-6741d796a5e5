import { css } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import { AppTheme, RDSButton, RDSTypography, RDSUploadFile } from "@roshn/ui-kit";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Input } from "~/components/form-components/input/input";
import { Select } from "~/components/form-components/select/select";
import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product-list/product-list";
import { AppPaths } from "~/utils/app-paths";

type InputPayload = {
  [key: string]: string;
};

type CustomAttribute = {
  label: string;
  value: string;
};

const formSchema = {
  "id": 1,
  "name": "Real Estate - Off-plan",
  "name_en": "Real Estate - Off-plan",
  "name_ar": "وحدات عقارية على الخارطة",
  "slug": "real-estate-off-plan",
  "template_id": "real_estate_off_plan_v1",
  "version": 1,
  "description": "Real Estate - Off-plan Projects",
  "description_en": "Real Estate - Off-plan Projects",
  "description_ar": "Real Estate - Off-plan Projects",
  "is_active": true,
  "order": 0,
  "marketplace": 1,
  "marketplace_merchant": null,
  "product_attributes": [
    {
      "id": 14,
      "name": "unitCode",
      "slug": "unitcode",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 0
    },
    {
      "id": 18,
      "name": "RegaLicenceNumber",
      "slug": "regalicencenumber",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 0
    },
    {
      "id": 15,
      "name": "propertyType",
      "slug": "propertytype",
      "attribute_type": "SELECT",
      "options": [
        "Townhouse",
        "Villa",
        "Apartment"
      ],
      "is_required": true,
      "is_translatable": false,
      "order": 1
    },
    {
      "id": 16,
      "name": "numberofBedrooms",
      "slug": "numberofbedrooms",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 2
    },
    {
      "id": 20,
      "name": "grossFloorAreaSqm",
      "slug": "grossfloorareasqm",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": false,
      "is_translatable": false,
      "order": 3
    },
    {
      "id": 17,
      "name": "numberofBathrooms",
      "slug": "numberofbathrooms",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 4
    }
  ],
  "category_attributes": [
    {
      "id": 1,
      "name": "projectName",
      "slug": "projectname",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 0,
      "value": null
    },
    {
      "id": 2,
      "name": "logo",
      "slug": "logo",
      "attribute_type": "IMAGE",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 1,
      "value": []
    },
    {
      "id": 3,
      "name": "address",
      "slug": "address",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 2,
      "value": null
    },
    {
      "id": 4,
      "name": "city",
      "slug": "city",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 3,
      "value": null
    },
    {
      "id": 5,
      "name": "handoverDate",
      "slug": "handoverdate",
      "attribute_type": "DATE",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 4,
      "value": null
    },
    {
      "id": 6,
      "name": "description",
      "slug": "description",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 5,
      "value": null
    },
    {
      "id": 7,
      "name": "nearbyAmenities",
      "slug": "nearbyamenities",
      "attribute_type": "MULTI_SELECT",
      "options": [
        "Dining & Entertainmen",
        "Health Centre",
        "Kindergarten",
        "Mosque",
        "Public Park",
        "Retail Centres",
        "School",
        "Sports Ground"
      ],
      "is_required": true,
      "is_translatable": true,
      "order": 6,
      "value": null
    },
    {
      "id": 8,
      "name": "paymentPlanReservationFeeRefundable",
      "slug": "paymentplanreservationfeerefundable",
      "attribute_type": "BOOLEAN",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 7,
      "value": null
    },
    {
      "id": 9,
      "name": "paymentPlanFeeType",
      "slug": "paymentplanfeetype",
      "attribute_type": "SELECT",
      "options": [
        "Fixed",
        "Percentage of Total"
      ],
      "is_required": true,
      "is_translatable": false,
      "order": 8,
      "value": null
    },
    {
      "id": 10,
      "name": "reservationFeeAmount",
      "slug": "reservationfeeamount",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 9,
      "value": null
    },
    {
      "id": 11,
      "name": "paymentPlanDownPayment",
      "slug": "paymentplandownpayment",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 10,
      "value": null
    },
    {
      "id": 12,
      "name": "paymentPlanHandover",
      "slug": "paymentplanhandover",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 11,
      "value": null
    },
    {
      "id": 13,
      "name": "paymentPlanDuringConstruction",
      "slug": "paymentplanduringconstruction",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 12,
      "value": null
    },
    {
      "id": 19,
      "name": "RegaLicenceNumber",
      "slug": "regalicencenumber",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 13,
      "value": null
    }
  ],
  "attributes_count": 20,
  "product_attributes_count": 6,
  "category_attributes_count": 14,
  "categories_using_template": [],
  "created_date": "2025-07-16T11:10:30.731498Z",
  "updated_date": "2025-07-16T11:14:21.906338Z",
  "marketplace_asset_categories": [
    {
      "id": 1,
      "name": "Brochures",
      "slug": "brochures",
      "description": "Brochure for the project",
      "allowed_file_types": [
        "pdf",
        "jpg",
        "png"
      ],
      "max_file_size": 5,
      "order": 0,
      "is_active": true,
      "assets_count": 0
    },
    {
      "id": 2,
      "name": "Floor Plans",
      "slug": "floor-plans",
      "description": "Floor Plans",
      "allowed_file_types": [
        "pdf",
        "jpg",
        "png"
      ],
      "max_file_size": 5,
      "order": 0,
      "is_active": true,
      "assets_count": 0
    },
    {
      "id": 3,
      "name": "Masterplan",
      "slug": "masterplan",
      "description": "masterplan",
      "allowed_file_types": [
        "pdf",
        "jpg",
        "png"
      ],
      "max_file_size": 5,
      "order": 0,
      "is_active": true,
      "assets_count": 0
    }
  ]
}

export type OutputPayload = {
  title: string;
  description: string;
  weight_unit: string;
  weight: string;
  price: string;
  sale_price: string;
  categories: string[];
  images: string[];
  variants: string[];
  modifier_groups: string[];
  combinations: string[];
  is_popular: boolean;
  cross_sell_groups: string[];
  title_ar: string;
  marketplace_categories: number[];
  per_item_quantity: number;
  stock_quantity: string | null;
  sku: string;
  custom_attributes: CustomAttribute[];
  status: string;
  manage_stock: boolean;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: css({
    flex: "0 0 30%",
  }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),
};

const schema = z.object({
  images: z.any(),
  title: z.any(),
  description: z.any(),
  category: z.any(),
  price: z.any(),
  salePrice: z.any(),
  per_item_quantity: z.any(),
  communityId: z.any(),
  endDate: z.any(),
  eventCommunityAR: z.any(),
  eventCommunityEN: z.any(),
  eventContactNumber: z.any(),
  eventDate: z.any(),
  eventDescriptionAR: z.any(),
  eventDescriptionEN: z.any(),
  eventRegistrationStatus: z.any(),
  eventTermsAndConditionsAR: z.any(),
  eventTermsAndConditionsEN: z.any(),
  eventTitleEN: z.any(),
  eventTitleAR: z.any(),
  locationLink: z.any(),
  maxAttendees: z.any(),
  totalEventGuests: z.any(),
});

const Section = ({ heading, children }: { heading: string; children: React.ReactNode }) => {
  return (
    <div css={styles.sectionWrapper}>
      <RDSTypography css={styles.sectionHeadingText}>{heading}</RDSTypography>
      <div css={styles.sectionDivider} />
      <div css={styles.sectionChildrenWrapper}>{children}</div>
    </div>
  );
};

export default function AddProductPage() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const productService = useInjection<ProductService>(ProductService);
  const [loading, setLoading] = useState(false);

  const { handleSubmit, control } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
  });

  const handleInventoryNav = () => {
    navigate(generateAppPath(AppPaths.inventory));
  };

  const transformPayload = (input: InputPayload): OutputPayload => {
    const {
      title,
      description,
      price,
      salePrice,
      per_item_quantity,
      eventTitleAR,
      category,
      maxAttendees,
      totalEventGuests,
      ...rest
    } = input;

    // Mapping keys to labels for custom_attributes
    const customAttributeMap: Record<string, string> = {
      communityId: "community-id",
      eventCommunityAR: "eventCommunityAr",
      eventCommunityEN: "eventCommunityEn",
      eventContactNumber: "eventContactNumber",
      eventDate: "eventDate",
      endDate: "end-date",
      eventDescriptionAR: "eventDescriptionAr",
      eventDescriptionEN: "eventDescriptionEn",
      eventRegistrationStatus: "eventRegistrationStatus",
      eventTermsAndConditionsAR: "tnrAr",
      eventTermsAndConditionsEN: "tnrEn",
      eventTitleAR: "eventTitleAr",
      eventTitleEN: "eventTitleEn",
      locationLink: "location-link",
      maxAttendees: "maxAttendees",
      totalEventGuests: "totalEventGuests",
    };

    const custom_attributes: CustomAttribute[] = Object.entries(rest)
      .filter(([key]) => key in customAttributeMap)
      .map(([key, value]) => ({
        label: customAttributeMap[key],
        value,
      }));

    return {
      title,
      description: description || "",
      weight_unit: "g",
      weight: "",
      price,
      sale_price: salePrice,
      categories: [],
      images: [],
      variants: [],
      modifier_groups: [],
      combinations: [],
      is_popular: false,
      cross_sell_groups: [],
      title_ar: eventTitleAR,
      marketplace_categories: [24],
      per_item_quantity: 123,
      stock_quantity: null,
      sku: "",
      custom_attributes,
      status: "ACTIVE",
      manage_stock: false,
    };
  };

  const transformAndSubmit = async (input: InputPayload) => {
    setLoading(true);
    const output = transformPayload(input);
    await productService.createProduct(output);
    setLoading(false);
    navigate(generateAppPath(AppPaths.inventory));
  };

  return (
    <div css={styles.wrapper}>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to inventories"
        leadIcon="left_arrow"
        onClick={handleInventoryNav}
      />
      <div css={styles.sectionsWrapper}>
        <form css={[styles.form, styles.sectionLayouts]} onSubmit={handleSubmit((args) => {})}>
          <Section heading="INVENTORY DETAILS">
            <RDSUploadFile label="Gallery" />
            <Input name="title" control={control} label="Title" isRequired placeholder="Title..." />
            <Input
              name="description"
              control={control}
              label="Description"
              isRequired
              placeholder="description..."
            />
            <Select
              control={control}
              name="category"
              label="Category"
              data-testid="category"
              isRequired
              placeholder="Select a category..."
              options={[
                { label: "Villa", value: "villa" },
                { label: "Town House", value: "town-house" },
                { label: "Duplex", value: "duplex" },
              ]}
            />
            <Input
              name="price"
              control={control}
              label="Price"
              isRequired
              placeholder="Enter price..."
            />
            <Input
              name="salePrice"
              control={control}
              label="Sale Price"
              isRequired
              placeholder="Enter sale price..."
            />
            <Input
              name="per_item_quantity"
              control={control}
              label="Product Unit"
              isRequired
              placeholder="Enter product unit..."
            />
            <RDSButton
              variant="primary"
              size="lg"
              text="Save Changes"
              onClick={handleSubmit((args) => {
                transformAndSubmit(args);
              })}
              loading={loading}
            />
          </Section>
        </form>
      </div>
    </div>
  );
}
