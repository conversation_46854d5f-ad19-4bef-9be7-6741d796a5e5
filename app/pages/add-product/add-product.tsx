import { css } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import { AppTheme, RDSButton, RDSTypography, RDSUploadFile, RDSCheckbox } from "@roshn/ui-kit";
import { useState, useMemo } from "react";
import { useForm, Controller } from "react-hook-form";
import { z } from "zod";

import { Input } from "~/components/form-components/input/input";
import { Select } from "~/components/form-components/select/select";
import { DatePicker } from "~/components/form-components/date-picker/date-picker";
import { TagSelector } from "~/components/form-components/tag-selector/tags-selector";
import { ButtonFileUpload } from "~/components/form-components/file-upload/button-file-upload";
import { TextArea } from "~/components/form-components/text-area/text-area";
import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product-list/product-list";
import { AppPaths } from "~/utils/app-paths";

type InputPayload = {
  [key: string]: string;
};

type CustomAttribute = {
  label: string;
  value: string;
};

const formSchema = {
  "id": 1,
  "name": "Real Estate - Off-plan",
  "name_en": "Real Estate - Off-plan",
  "name_ar": "وحدات عقارية على الخارطة",
  "slug": "real-estate-off-plan",
  "template_id": "real_estate_off_plan_v1",
  "version": 1,
  "description": "Real Estate - Off-plan Projects",
  "description_en": "Real Estate - Off-plan Projects",
  "description_ar": "Real Estate - Off-plan Projects",
  "is_active": true,
  "order": 0,
  "marketplace": 1,
  "marketplace_merchant": null,
  "product_attributes": [
    {
      "id": 14,
      "name": "unitCode",
      "slug": "unitcode",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 0
    },
    {
      "id": 18,
      "name": "RegaLicenceNumber",
      "slug": "regalicencenumber",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 0
    },
    {
      "id": 15,
      "name": "propertyType",
      "slug": "propertytype",
      "attribute_type": "SELECT",
      "options": [
        "Townhouse",
        "Villa",
        "Apartment"
      ],
      "is_required": true,
      "is_translatable": false,
      "order": 1
    },
    {
      "id": 16,
      "name": "numberofBedrooms",
      "slug": "numberofbedrooms",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 2
    },
    {
      "id": 20,
      "name": "grossFloorAreaSqm",
      "slug": "grossfloorareasqm",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": false,
      "is_translatable": false,
      "order": 3
    },
    {
      "id": 17,
      "name": "numberofBathrooms",
      "slug": "numberofbathrooms",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 4
    }
  ],
  "category_attributes": [
    {
      "id": 1,
      "name": "projectName",
      "slug": "projectname",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 0,
      "value": null
    },
    {
      "id": 2,
      "name": "logo",
      "slug": "logo",
      "attribute_type": "IMAGE",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 1,
      "value": []
    },
    {
      "id": 3,
      "name": "address",
      "slug": "address",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 2,
      "value": null
    },
    {
      "id": 4,
      "name": "city",
      "slug": "city",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 3,
      "value": null
    },
    {
      "id": 5,
      "name": "handoverDate",
      "slug": "handoverdate",
      "attribute_type": "DATE",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 4,
      "value": null
    },
    {
      "id": 6,
      "name": "description",
      "slug": "description",
      "attribute_type": "TEXT",
      "options": [],
      "is_required": true,
      "is_translatable": true,
      "order": 5,
      "value": null
    },
    {
      "id": 7,
      "name": "nearbyAmenities",
      "slug": "nearbyamenities",
      "attribute_type": "MULTI_SELECT",
      "options": [
        "Dining & Entertainmen",
        "Health Centre",
        "Kindergarten",
        "Mosque",
        "Public Park",
        "Retail Centres",
        "School",
        "Sports Ground"
      ],
      "is_required": true,
      "is_translatable": true,
      "order": 6,
      "value": null
    },
    {
      "id": 8,
      "name": "paymentPlanReservationFeeRefundable",
      "slug": "paymentplanreservationfeerefundable",
      "attribute_type": "BOOLEAN",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 7,
      "value": null
    },
    {
      "id": 9,
      "name": "paymentPlanFeeType",
      "slug": "paymentplanfeetype",
      "attribute_type": "SELECT",
      "options": [
        "Fixed",
        "Percentage of Total"
      ],
      "is_required": true,
      "is_translatable": false,
      "order": 8,
      "value": null
    },
    {
      "id": 10,
      "name": "reservationFeeAmount",
      "slug": "reservationfeeamount",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 9,
      "value": null
    },
    {
      "id": 11,
      "name": "paymentPlanDownPayment",
      "slug": "paymentplandownpayment",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 10,
      "value": null
    },
    {
      "id": 12,
      "name": "paymentPlanHandover",
      "slug": "paymentplanhandover",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 11,
      "value": null
    },
    {
      "id": 13,
      "name": "paymentPlanDuringConstruction",
      "slug": "paymentplanduringconstruction",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 12,
      "value": null
    },
    {
      "id": 19,
      "name": "RegaLicenceNumber",
      "slug": "regalicencenumber",
      "attribute_type": "NUMBER",
      "options": [],
      "is_required": true,
      "is_translatable": false,
      "order": 13,
      "value": null
    }
  ],
  "attributes_count": 20,
  "product_attributes_count": 6,
  "category_attributes_count": 14,
  "categories_using_template": [],
  "created_date": "2025-07-16T11:10:30.731498Z",
  "updated_date": "2025-07-16T11:14:21.906338Z",
  "marketplace_asset_categories": [
    {
      "id": 1,
      "name": "Brochures",
      "slug": "brochures",
      "description": "Brochure for the project",
      "allowed_file_types": [
        "pdf",
        "jpg",
        "png"
      ],
      "max_file_size": 5,
      "order": 0,
      "is_active": true,
      "assets_count": 0
    },
    {
      "id": 2,
      "name": "Floor Plans",
      "slug": "floor-plans",
      "description": "Floor Plans",
      "allowed_file_types": [
        "pdf",
        "jpg",
        "png"
      ],
      "max_file_size": 5,
      "order": 0,
      "is_active": true,
      "assets_count": 0
    },
    {
      "id": 3,
      "name": "Masterplan",
      "slug": "masterplan",
      "description": "masterplan",
      "allowed_file_types": [
        "pdf",
        "jpg",
        "png"
      ],
      "max_file_size": 5,
      "order": 0,
      "is_active": true,
      "assets_count": 0
    }
  ]
}

export type OutputPayload = {
  title: string;
  description: string;
  weight_unit: string;
  weight: string;
  price: string;
  sale_price: string;
  categories: string[];
  images: string[];
  variants: string[];
  modifier_groups: string[];
  combinations: string[];
  is_popular: boolean;
  cross_sell_groups: string[];
  title_ar: string;
  marketplace_categories: number[];
  per_item_quantity: number;
  stock_quantity: string | null;
  sku: string;
  custom_attributes: CustomAttribute[];
  status: string;
  manage_stock: boolean;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: css({
    flex: "0 0 30%",
  }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),
};

// Helper function to create Zod schema based on attribute type
const createZodFieldSchema = (attribute: any) => {
  const { attribute_type, is_required, options } = attribute;

  let schema: z.ZodTypeAny;

  switch (attribute_type) {
    case 'TEXT':
      schema = z.string();
      break;
    case 'NUMBER':
      schema = z.coerce.number();
      break;
    case 'SELECT':
      schema = z.string();
      break;
    case 'MULTI_SELECT':
      schema = z.array(z.string());
      break;
    case 'BOOLEAN':
      schema = z.boolean();
      break;
    case 'DATE':
      schema = z.date();
      break;
    case 'IMAGE':
      schema = z.array(z.any());
      break;
    default:
      schema = z.any();
  }

  // Make field optional if not required
  if (!is_required) {
    schema = schema.optional();
  }

  return schema;
};

// Generate dynamic schema based on formSchema
const generateDynamicSchema = (formSchemaData: typeof formSchema) => {
  const schemaFields: Record<string, z.ZodTypeAny> = {
    // Base product fields
    images: z.array(z.any()).optional(),
    title: z.string().min(1, "Title is required"),
    description: z.string().min(1, "Description is required"),
    category: z.string().min(1, "Category is required"),
    price: z.coerce.number().min(0, "Price must be positive"),
    salePrice: z.coerce.number().min(0, "Sale price must be positive").optional(),
    per_item_quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  };

  // Add product attributes to schema
  formSchemaData.product_attributes.forEach((attr) => {
    schemaFields[attr.slug] = createZodFieldSchema(attr);
  });

  // Add category attributes to schema
  formSchemaData.category_attributes.forEach((attr) => {
    schemaFields[attr.slug] = createZodFieldSchema(attr);
  });

  return z.object(schemaFields);
};

const Section = ({ heading, children }: { heading: string; children: React.ReactNode }) => {
  return (
    <div css={styles.sectionWrapper}>
      <RDSTypography css={styles.sectionHeadingText}>{heading}</RDSTypography>
      <div css={styles.sectionDivider} />
      <div css={styles.sectionChildrenWrapper}>{children}</div>
    </div>
  );
};

export default function AddProductPage() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const productService = useInjection<ProductService>(ProductService);
  const [loading, setLoading] = useState(false);

  // Generate dynamic schema based on formSchema
  const dynamicSchema = useMemo(() => generateDynamicSchema(formSchema), []);

  const { handleSubmit, control } = useForm({
    resolver: zodResolver(dynamicSchema),
    mode: "onChange",
  });

  const handleInventoryNav = () => {
    navigate(generateAppPath(AppPaths.inventory));
  };

  const toHumanReadable = (label: string) => {
    return label
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .replace(/^./, str => str.toUpperCase());
  }

  // Function to render form field based on attribute type
  const renderFormField = (attribute: any) => {
    const { id, name, slug, attribute_type, options, is_required } = attribute;
    const key = `${slug}-${id}`;

    switch (attribute_type) {
      case 'TEXT':
        // Use TextArea for description fields or fields that might contain longer text
        const isDescriptionField = name.toLowerCase().includes('description');

        return isDescriptionField ? (
          <TextArea
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            placeholder={`Enter ${name.toLowerCase()}...`}
            rows={4}
          />
        ) : (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Enter ${name.toLowerCase()}...`}
          />
        );

      case 'NUMBER':
        return (
          <Input
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            type="number"
            isRequired={is_required}
            placeholder={`Enter ${name.toLowerCase()}...`}
          />
        );

      case 'SELECT':
        return (
          <Select
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Select ${name.toLowerCase()}...`}
            options={options.map((option: string) => ({
              label: option,
              value: option.toLowerCase().replace(/\s+/g, '-')
            }))}
          />
        );

      case 'MULTI_SELECT':
        return (
          <TagSelector
            key={key}
            name={slug}
            control={control}
            label={toHumanReadable(name)}
            description={`Select multiple ${name.toLowerCase()}`}
            options={options.map((option: string) => ({
              label: option,
              value: option.toLowerCase().replace(/\s+/g, '-')
            }))}
          />
        );

      case 'BOOLEAN':
        return (
          <Controller
            key={key}
            name={slug}
            control={control}
            render={({ field: { value, onChange } }) => (
              <RDSCheckbox
                label={toHumanReadable(name)}
                checked={value || false}
                onChange={(checked) => onChange(checked)}
              />
            )}
          />
        );

      case 'DATE':
        return (
          <DatePicker
            key={key}
            name={slug}
            control={control}
            startDate={new Date()}
            dateInputProps={{
              helperText: `Select ${name.toLowerCase()}`
            }}
          />
        );

      case 'IMAGE':
        return (
          <ButtonFileUpload
            key={key}
            name={toHumanReadable(slug)}
            control={control}
            label={`Upload ${name}`}
            accept="image/*"
            multiple={true}
          />
        );

      default:
        return (
          <Input
            key={key}
            name={toHumanReadable(slug)}
            control={control}
            label={toHumanReadable(name)}
            isRequired={is_required}
            placeholder={`Enter ${name.toLowerCase()}...`}
          />
        );
    }
  };

  const transformPayload = (input: InputPayload): OutputPayload => {
    const {
      title,
      description,
      price,
      salePrice,
      per_item_quantity,
      category,
      images,
      ...rest
    } = input;

    // Create custom attributes from product_attributes and category_attributes
    const custom_attributes: CustomAttribute[] = [];

    // Process product attributes
    formSchema.product_attributes.forEach((attr) => {
      const value = rest[attr.slug];
      if (value !== undefined && value !== null && value !== '') {
        custom_attributes.push({
          label: attr.slug,
          value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        });
      }
    });

    // Process category attributes
    formSchema.category_attributes.forEach((attr) => {
      const value = rest[attr.slug];
      if (value !== undefined && value !== null && value !== '') {
        custom_attributes.push({
          label: attr.slug,
          value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        });
      }
    });

    return {
      title,
      description: description || "",
      weight_unit: "g",
      weight: "",
      price: String(price),
      sale_price: salePrice ? String(salePrice) : "",
      categories: [],
      images: Array.isArray(images) ? images : [],
      variants: [],
      modifier_groups: [],
      combinations: [],
      is_popular: false,
      cross_sell_groups: [],
      title_ar: "", // You can map this from category attributes if needed
      marketplace_categories: [formSchema.id], // Use the template ID
      per_item_quantity: Number(per_item_quantity),
      stock_quantity: null,
      sku: "", // Generate or get from form if needed
      custom_attributes,
      status: "ACTIVE",
      manage_stock: false,
    };
  };

  const transformAndSubmit = async (input: InputPayload) => {
    setLoading(true);
    const output = transformPayload(input);
    await productService.createProduct(output);
    setLoading(false);
    navigate(generateAppPath(AppPaths.inventory));
  };

  return (
    <div css={styles.wrapper}>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to inventories"
        leadIcon="left_arrow"
        onClick={handleInventoryNav}
      />
      <div css={styles.sectionsWrapper}>
        <form css={[styles.form, styles.sectionLayout]} onSubmit={handleSubmit(transformAndSubmit)}>

          {/* Product Attributes */}
          {formSchema.product_attributes.length > 0 && (
            <Section heading="PRODUCT ATTRIBUTES">
              {formSchema.product_attributes
                .sort((a, b) => a.order - b.order)
                .map(renderFormField)}
            </Section>
          )}

          {/* Category Attributes */}
          {formSchema.category_attributes.length > 0 && (
            <Section heading="CATEGORY ATTRIBUTES">
              {formSchema.category_attributes
                .sort((a, b) => a.order - b.order)
                .map(renderFormField)}
            </Section>
          )}

          {/* Submit Button */}
          <RDSButton
            variant="primary"
            size="lg"
            text="Save Product"
            type="submit"
            loading={loading}
          />
        </form>
      </div>
    </div>
  );
}
