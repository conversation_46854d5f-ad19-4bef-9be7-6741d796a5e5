import { RDS<PERSON>tepper, RDSTextInputProps } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledTextInputProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  type?: string;
} & Omit<RDSTextInputProps, "name" | "value" | "onChange" | "onBlur">;

export function NumberInput<T extends FieldValues>({
  name,
  control,
  label,
  placeholder,
  type = "text",
  helperText,
  ...rest
}: ControlledTextInputProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <RDSTextInput
          {...field}
          label={label}
          placeholder={placeholder}
          type={type}
          helperText={fieldState.error?.message ?? helperText}
          isInvalid={!!fieldState.error}
          {...rest}
        />
      )}
    />
  );
}
